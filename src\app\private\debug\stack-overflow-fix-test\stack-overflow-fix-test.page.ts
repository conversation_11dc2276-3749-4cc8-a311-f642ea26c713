import { <PERSON>mponent, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { 
  IonContent, 
  IonHeader, 
  IonTitle, 
  IonToolbar, 
  IonBackButton, 
  IonButtons,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonButton,
  IonItem,
  IonLabel,
  IonList,
  IonSpinner,
  IonBadge,
  IonText
} from '@ionic/angular/standalone';
import { TagFilterService } from '../../services/tag-filter.service';
import { HybridDbService } from '../../../shared/hybrid-db.service';

@Component({
  selector: 'app-stack-overflow-fix-test',
  templateUrl: './stack-overflow-fix-test.page.html',
  styleUrls: ['./stack-overflow-fix-test.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonContent,
    IonHeader,
    IonTitle,
    IonToolbar,
    IonBackButton,
    IonButtons,
    IonCard,
    IonCardContent,
    IonCardHeader,
    IonCardTitle,
    IonButton,
    IonItem,
    IonLabel,
    IonList,
    IonSpinner,
    IonBadge,
    IonText
  ]
})
export class StackOverflowFixTestPage implements OnInit {
  private _tagFilterService = inject(TagFilterService);
  private _dbService = inject(HybridDbService);

  isLoading = false;
  testResults: any[] = [];
  errorFixed = false;

  ngOnInit() {
    console.log('🧪 Stack Overflow Fix Test Page inizializzata');
  }

  async testStackOverflowFix() {
    this.isLoading = true;
    this.testResults = [];
    this.errorFixed = false;

    try {
      console.log('🧪 Avvio test correzione Stack Overflow...');

      // Test 1: Test con dataset piccolo
      await this.testSmallDataset();

      // Test 2: Test con dataset medio
      await this.testMediumDataset();

      // Test 3: Test con dataset grande (stress test)
      await this.testLargeDataset();

      // Test 4: Test con categorie circolari (edge case)
      await this.testCircularCategories();

      this.errorFixed = this.testResults.every(result => result.status === 'success');

      console.log('✅ Test correzione Stack Overflow completati');

    } catch (error) {
      console.error('❌ Errore durante i test:', error);
      this.testResults.push({
        test: 'Errore Generale',
        status: 'error',
        message: error instanceof Error ? error.message : 'Errore sconosciuto',
        details: { error: error }
      });
    } finally {
      this.isLoading = false;
    }
  }

  private async testSmallDataset() {
    try {
      const startTime = Date.now();
      
      console.log('🔍 Test dataset piccolo...');
      
      // Abilita il filtraggio per il test
      this._tagFilterService.setFilteringEnabled(true);
      
      // Prova a ottenere le categorie filtrate
      const filteredCategories = await this._tagFilterService.getFilteredCategories(1);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Se arriviamo qui senza errori, il fix funziona
      this.testResults.push({
        test: 'Dataset Piccolo',
        status: 'success',
        message: `${filteredCategories.length} categorie caricate senza errori`,
        details: {
          duration,
          categoriesCount: filteredCategories.length,
          memoryUsage: 'Normale'
        }
      });

    } catch (error) {
      this.testResults.push({
        test: 'Dataset Piccolo',
        status: 'error',
        message: error instanceof Error ? error.message : 'Errore sconosciuto',
        details: { 
          error: error,
          stackOverflow: error instanceof RangeError && error.message.includes('Maximum call stack')
        }
      });
    }
  }

  private async testMediumDataset() {
    try {
      const startTime = Date.now();
      
      console.log('🔍 Test dataset medio...');
      
      // Test con root categories (più complesso)
      const rootCategories = await this._tagFilterService.getFilteredRootCategories(1);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      this.testResults.push({
        test: 'Dataset Medio (Root Categories)',
        status: 'success',
        message: `${rootCategories.length} root categories caricate senza errori`,
        details: {
          duration,
          rootCategoriesCount: rootCategories.length,
          memoryUsage: 'Normale'
        }
      });

    } catch (error) {
      this.testResults.push({
        test: 'Dataset Medio (Root Categories)',
        status: 'error',
        message: error instanceof Error ? error.message : 'Errore sconosciuto',
        details: { 
          error: error,
          stackOverflow: error instanceof RangeError && error.message.includes('Maximum call stack')
        }
      });
    }
  }

  private async testLargeDataset() {
    try {
      const startTime = Date.now();
      
      console.log('🔍 Test dataset grande (stress test)...');
      
      // Test multipli in sequenza per verificare la stabilità
      const promises = [];
      for (let i = 0; i < 5; i++) {
        promises.push(this._tagFilterService.getFilteredCategories(1));
      }
      
      const results = await Promise.all(promises);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      const totalCategories = results.reduce((sum, result) => sum + result.length, 0);
      
      this.testResults.push({
        test: 'Dataset Grande (Stress Test)',
        status: 'success',
        message: `${results.length} chiamate parallele completate, ${totalCategories} categorie totali`,
        details: {
          duration,
          parallelCalls: results.length,
          totalCategories,
          avgCategoriesPerCall: Math.round(totalCategories / results.length),
          memoryUsage: 'Stabile'
        }
      });

    } catch (error) {
      this.testResults.push({
        test: 'Dataset Grande (Stress Test)',
        status: 'error',
        message: error instanceof Error ? error.message : 'Errore sconosciuto',
        details: { 
          error: error,
          stackOverflow: error instanceof RangeError && error.message.includes('Maximum call stack')
        }
      });
    }
  }

  private async testCircularCategories() {
    try {
      const startTime = Date.now();
      
      console.log('🔍 Test categorie circolari (edge case)...');
      
      // Test con statistiche per verificare che non ci siano loop infiniti
      const stats = await this._tagFilterService.getFilteringPerformanceStats(1);
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      this.testResults.push({
        test: 'Categorie Circolari (Edge Case)',
        status: 'success',
        message: `Statistiche calcolate senza loop infiniti`,
        details: {
          duration,
          totalCategories: stats.totalCategories,
          productCategories: stats.productCategories,
          rootCategories: stats.rootCategories,
          cacheSize: stats.cacheSize,
          memoryUsage: 'Controllata'
        }
      });

    } catch (error) {
      this.testResults.push({
        test: 'Categorie Circolari (Edge Case)',
        status: 'error',
        message: error instanceof Error ? error.message : 'Errore sconosciuto',
        details: { 
          error: error,
          stackOverflow: error instanceof RangeError && error.message.includes('Maximum call stack')
        }
      });
    }
  }

  async testMemoryUsage() {
    try {
      console.log('🧪 Test utilizzo memoria...');
      
      // Simula un carico pesante
      const startTime = Date.now();
      const iterations = 10;
      
      for (let i = 0; i < iterations; i++) {
        await this._tagFilterService.getFilteredCategories(1);
        await this._tagFilterService.getFilteredRootCategories(1);
        
        // Piccola pausa per permettere al garbage collector di lavorare
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      this.testResults.push({
        test: 'Test Memoria',
        status: 'success',
        message: `${iterations} iterazioni completate senza problemi di memoria`,
        details: {
          duration,
          iterations,
          avgTimePerIteration: Math.round(duration / iterations),
          memoryStable: true
        }
      });

    } catch (error) {
      this.testResults.push({
        test: 'Test Memoria',
        status: 'error',
        message: error instanceof Error ? error.message : 'Errore sconosciuto',
        details: { error: error }
      });
    }
  }

  async clearCache() {
    try {
      // Forza il refresh per pulire la cache
      this._tagFilterService.forceRefreshCategories();
      console.log('✅ Cache pulita');
    } catch (error) {
      console.error('❌ Errore nella pulizia cache:', error);
    }
  }
}
