<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/private/debug"></ion-back-button>
    </ion-buttons>
    <ion-title>🔧 Fix Stack Overflow</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Fix Stack Overflow</ion-title>
    </ion-toolbar>
  </ion-header>

  <!-- Stato Fix -->
  <ion-card>
    <ion-card-header>
      <ion-card-title>🛠️ Stato Correzione</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-item>
        <ion-label>
          <h3>Stack Overflow Risolto</h3>
          <p>{{ errorFixed ? 'Sì - Tutti i test passati' : 'In verifica...' }}</p>
        </ion-label>
        <ion-badge [color]="errorFixed ? 'success' : 'warning'">
          {{ errorFixed ? 'RISOLTO' : 'TEST' }}
        </ion-badge>
      </ion-item>
    </ion-card-content>
  </ion-card>

  <!-- Controlli Test -->
  <ion-card>
    <ion-card-header>
      <ion-card-title>🧪 Test Correzione</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <div class="test-buttons">
        <ion-button 
          (click)="testStackOverflowFix()" 
          [disabled]="isLoading"
          color="primary"
          expand="block">
          <ion-spinner *ngIf="isLoading" name="crescent"></ion-spinner>
          {{ isLoading ? 'Test in corso...' : 'Esegui Test Correzione' }}
        </ion-button>

        <ion-button 
          (click)="testMemoryUsage()" 
          [disabled]="isLoading"
          color="secondary"
          expand="block">
          Test Utilizzo Memoria
        </ion-button>

        <ion-button 
          (click)="clearCache()" 
          [disabled]="isLoading"
          color="tertiary"
          expand="block">
          Pulisci Cache
        </ion-button>
      </div>
    </ion-card-content>
  </ion-card>

  <!-- Risultati Test -->
  <ion-card *ngIf="testResults.length > 0">
    <ion-card-header>
      <ion-card-title>📊 Risultati Test</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-list>
        <ion-item *ngFor="let result of testResults">
          <ion-label>
            <h3>{{ result.test }}</h3>
            <p>
              <ion-text [color]="result.status === 'success' ? 'success' : 'danger'">
                <strong>{{ result.status === 'success' ? '✅' : '❌' }}</strong>
                {{ result.message }}
              </ion-text>
            </p>
            <div *ngIf="result.details" class="details">
              <p *ngIf="result.details.duration"><small><strong>Durata:</strong> {{ result.details.duration }}ms</small></p>
              <p *ngIf="result.details.categoriesCount"><small><strong>Categorie:</strong> {{ result.details.categoriesCount }}</small></p>
              <p *ngIf="result.details.rootCategoriesCount"><small><strong>Root Categories:</strong> {{ result.details.rootCategoriesCount }}</small></p>
              <p *ngIf="result.details.parallelCalls"><small><strong>Chiamate Parallele:</strong> {{ result.details.parallelCalls }}</small></p>
              <p *ngIf="result.details.totalCategories"><small><strong>Categorie Totali:</strong> {{ result.details.totalCategories }}</small></p>
              <p *ngIf="result.details.avgCategoriesPerCall"><small><strong>Media per Chiamata:</strong> {{ result.details.avgCategoriesPerCall }}</small></p>
              <p *ngIf="result.details.iterations"><small><strong>Iterazioni:</strong> {{ result.details.iterations }}</small></p>
              <p *ngIf="result.details.avgTimePerIteration"><small><strong>Tempo Medio:</strong> {{ result.details.avgTimePerIteration }}ms</small></p>
              <p *ngIf="result.details.memoryUsage"><small><strong>Memoria:</strong> {{ result.details.memoryUsage }}</small></p>
              <p *ngIf="result.details.stackOverflow" class="error"><small><strong>⚠️ Stack Overflow Rilevato!</strong></small></p>
            </div>
          </ion-label>
        </ion-item>
      </ion-list>
    </ion-card-content>
  </ion-card>

  <!-- Informazioni Tecniche -->
  <ion-card>
    <ion-card-header>
      <ion-card-title>🔧 Dettagli Tecnici</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-text>
        <h4>Problema Risolto:</h4>
        <p>Il metodo <code>hasVisibleChildrenInSet</code> causava ricorsione infinita quando verificava le relazioni padre-figlio nelle categorie.</p>
        
        <h4>Soluzione Implementata:</h4>
        <ul>
          <li><strong>Algoritmo Non-Ricorsivo:</strong> Sostituito con approccio iterativo usando code</li>
          <li><strong>Mappa Relazioni:</strong> Pre-costruzione delle relazioni padre-figlio per efficienza</li>
          <li><strong>Propagazione Visibilità:</strong> Algoritmo ottimizzato per propagare la visibilità verso l'alto</li>
          <li><strong>Prevenzione Cicli:</strong> Set di controllo per evitare loop infiniti</li>
        </ul>
        
        <h4>Benefici:</h4>
        <ul>
          <li>✅ Eliminazione Stack Overflow</li>
          <li>✅ Performance migliorate</li>
          <li>✅ Utilizzo memoria controllato</li>
          <li>✅ Gestione edge cases</li>
        </ul>
      </ion-text>
    </ion-card-content>
  </ion-card>

</ion-content>
