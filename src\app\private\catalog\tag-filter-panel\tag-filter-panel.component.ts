import { Component, OnInit, On<PERSON><PERSON>roy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';
import { TagFilterService } from '../../services/tag-filter.service';
import {
  CustomerTagFilter,
  TagFilterState,
  Tag
} from '../../model/sector-hierarchy-response';

// Material imports (same as assign-tag-dialog)
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatIconModule } from '@angular/material/icon';
import { MatSlideToggleChange } from '@angular/material/slide-toggle';

@Component({
  selector: 'app-tag-filter-panel',
  templateUrl: './tag-filter-panel.component.html',
  styleUrls: ['./tag-filter-panel.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    MatButtonModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatSlideToggleModule,
    MatProgressSpinnerModule,
    MatIconModule
  ]
})
export class TagFilterPanelComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  
  // Services
  private _tagFilterService = inject(TagFilterService);

  // State
  currentFilter: CustomerTagFilter | null = null;
  isFilteringEnabled = false;
  isLoading = false;

  // Tag hierarchy data
  sectorHierarchy: Tag[] = [];
  availableTags: Tag[] = [];

  // UI state
  isExpanded = false;
  selectedCustomTags: string[] = [];
  isApplyingFilters = false;

  ngOnInit() {
    this.initializeComponent();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private async initializeComponent() {
    try {
      // Subscribe to filter state changes
      this._tagFilterService.filterState$
        .pipe(takeUntil(this.destroy$))
        .subscribe(state => {
          this.updateComponentState(state);
        });

      // Load sector hierarchy
      await this.loadSectorHierarchy();

    } catch (error) {
      console.error('❌ Error initializing tag filter panel:', error);
    }
  }

  private updateComponentState(state: TagFilterState) {
    this.currentFilter = state.currentFilter;
    this.isFilteringEnabled = state.isFilteringEnabled;
    
    if (this.currentFilter) {
      this.selectedCustomTags = [...this.currentFilter.customTags];
    }
  }

  private async loadSectorHierarchy() {
    try {
      this.isLoading = true;

      // TODO: Get catalog ID from configuration
      const catalogId = 1;

      // Carica dal database locale (i dati vengono sincronizzati tramite Sync v2)
      const content = await this._tagFilterService.getSectorHierarchyFromDatabase(catalogId);
      this.sectorHierarchy = content.tags || [];
      this.flattenTagHierarchy();

      console.log(`✅ Gerarchia settori caricata dal database locale per catalogo ${catalogId}`);

    } catch (error) {
      console.error('❌ Errore nel caricamento della gerarchia settori dal database:', error);
      this.sectorHierarchy = [];
    } finally {
      this.isLoading = false;
    }
  }

  private flattenTagHierarchy() {
    this.availableTags = [];
    this.flattenTags(this.sectorHierarchy);
  }

  private flattenTags(tags: Tag[]) {
    for (const tag of tags) {
      this.availableTags.push(tag);
      if (tag.subTags && tag.subTags.length > 0) {
        this.flattenTags(tag.subTags);
      }
    }
  }

  /**
   * Toggle the filtering on/off
   */
  async toggleFiltering() {
    try {
      this._tagFilterService.setFilteringEnabled(!this.isFilteringEnabled);
    } catch (error) {
      console.error('❌ Error toggling filtering:', error);
    }
  }

  /**
   * Toggle tag selection (used by mat-slide-toggle)
   */
  async toggleTagSelection(keyTag: string, event: MatSlideToggleChange) {
    try {
      if (event.checked) {
        await this.addCustomTag(keyTag);
      } else {
        await this.removeCustomTag(keyTag);
      }
    } catch (error) {
    }
  }

  /**
   * Add a custom tag to the filter
   */
  async addCustomTag(keyTag: string) {
    try {
      if (!this.canAddTag(keyTag)) {
        return;
      }

      await this._tagFilterService.addCustomTag(keyTag);

    } catch (error) {
    }
  }

  /**
   * Remove a custom tag from the filter
   */
  async removeCustomTag(keyTag: string) {
    try {
      if (!this.canRemoveTag(keyTag)) {
        console.warn(`Cannot remove predefined tag: ${keyTag}`);
        return;
      }

      await this._tagFilterService.removeCustomTag(keyTag);
      console.log(`✅ Removed custom tag: ${keyTag}`);

    } catch (error) {
      console.error('❌ Error removing custom tag:', error);
    }
  }

  /**
   * Check if a tag can be added
   */
  private canAddTag(keyTag: string): boolean {
    if (!this.currentFilter) {
      return false;
    }

    // Check if tag is already in custom tags
    if (this.currentFilter.customTags.includes(keyTag)) {
      return false;
    }

    // Check if tag is already a predefined tag
    const predefinedTags = [
      this.currentFilter.settore,
      this.currentFilter.attivita,
      this.currentFilter.professione,
      'UNIVERSALE'
    ].filter(Boolean);

    return !predefinedTags.includes(keyTag);
  }

  /**
   * Check if a tag can be removed
   */
  private canRemoveTag(keyTag: string): boolean {
    if (!this.currentFilter) {
      return false;
    }

    // Check if it's a predefined tag (cannot be removed)
    const predefinedTags = [
      this.currentFilter.settore,
      this.currentFilter.attivita,
      this.currentFilter.professione,
      'UNIVERSALE'
    ].filter(Boolean);

    return !predefinedTags.includes(keyTag);
  }

  /**
   * Check if a tag is currently active
   */
  isTagActive(keyTag: string): boolean {
    if (!this.currentFilter) {
      return false;
    }

    const allActiveTags = [
      this.currentFilter.settore,
      this.currentFilter.attivita,
      this.currentFilter.professione,
      'UNIVERSALE',
      ...this.currentFilter.customTags
    ].filter(Boolean);

    return allActiveTags.includes(keyTag);
  }

  /**
   * Check if a tag is predefined (cannot be removed)
   */
  isTagPredefined(keyTag: string): boolean {
    if (!this.currentFilter) {
      return false;
    }

    const predefinedTags = [
      this.currentFilter.settore,
      this.currentFilter.attivita,
      this.currentFilter.professione,
      'UNIVERSALE'
    ].filter(Boolean);

    return predefinedTags.includes(keyTag);
  }

  /**
   * Get the display name for a tag level
   */
  getTagLevelName(level: number): string {
    switch (level) {
      case 1: return 'Professione';
      case 2: return 'Attività';
      case 3: return 'Settore';
      default: return 'Tag';
    }
  }

  /**
   * Toggle the panel expansion
   */
  toggleExpansion() {
    this.isExpanded = !this.isExpanded;
  }

  /**
   * Get current filter summary for display
   */
  getFilterSummary(): string {
    if (!this.currentFilter) {
      return 'Nessun filtro attivo';
    }

    const activeTags = [
      this.currentFilter.settore,
      this.currentFilter.attivita,
      this.currentFilter.professione,
      ...this.currentFilter.customTags
    ].filter(Boolean);

    if (activeTags.length === 0) {
      return 'Solo tag Universale';
    }

    return `${activeTags.length} tag attivi`;
  }

  /**
   * Applica i filtri selezionati al catalogo
   */
  async applyFilters() {
    try {
      this.isApplyingFilters = true;

      console.log('🔄 [TAG_FILTER_PANEL] Applicazione filtri in corso...');

      // Salva la configurazione corrente se necessario
      if (this.currentFilter) {
        await this._tagFilterService.saveCustomerFilterState(
          this.currentFilter.customerUid,
          this.currentFilter
        );
      }

      // Forza il refresh delle categorie
      this._tagFilterService.forceRefreshCategories();

      console.log('✅ [TAG_FILTER_PANEL] Filtri applicati con successo - categorie in aggiornamento');

      // Simula un breve delay per UX
      await new Promise(resolve => setTimeout(resolve, 800));

    } catch (error) {
      console.error('❌ Errore nell\'applicazione dei filtri:', error);
    } finally {
      this.isApplyingFilters = false;
    }
  }

  /**
   * Resetta tutti i filtri personalizzati
   */
  async resetFilters() {
    try {
      console.log('🔄 Reset filtri in corso...');

      if (!this.currentFilter) {
        return;
      }

      // Rimuove tutti i tag personalizzati
      const customTagsToRemove = [...this.currentFilter.customTags];
      for (const tag of customTagsToRemove) {
        await this.removeCustomTag(tag);
      }

      console.log('✅ Filtri resettati con successo');

    } catch (error) {
      console.error('❌ Errore nel reset dei filtri:', error);
    }
  }
}
