.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.test-buttons ion-button {
  margin: 0;
}

.button-row {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.button-row ion-button {
  flex: 1;
}

ion-card {
  margin: 16px;
}

ion-card-title {
  font-size: 1.2em;
  font-weight: 600;
}

.details {
  margin-top: 8px;
  padding: 8px;
  background: var(--ion-color-light);
  border-radius: 4px;
}

.details p {
  margin: 2px 0;
}

.details small {
  color: var(--ion-color-medium);
}

ion-text[color="success"] {
  color: var(--ion-color-success);
}

ion-text[color="warning"] {
  color: var(--ion-color-warning);
}

ion-text[color="danger"] {
  color: var(--ion-color-danger);
}
