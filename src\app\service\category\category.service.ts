import { inject, Injectable } from '@angular/core';
import { HybridDbService } from 'src/app/shared/hybrid-db.service';
import Utils from 'src/app/shared/utils';
import { Category } from '../data/category';
import { Selection } from 'src/app/shared/selection';
import { Subcategory } from '../data/subcategory';
import { TagFilterService } from '../../private/services/tag-filter.service';
import { CategoryFilterResult } from '../../private/model/sector-hierarchy-response';

@Injectable({
  providedIn: 'root'
})
export class CategoryService {
  private categories: Category[] = [];
  private datacolCategories: Category[] = [];
  private _dbService = inject(HybridDbService);
  private _tagFilterService = inject(TagFilterService);

  async getFilteredCategories(ids: string[]){
    await this._dbService.getAll(["categories"], ['id', 'name', 'image']).then((categories: Category[]) => {
      this.categories = categories;
    });
    this.explodeHierarchy(this.categories);
    this.datacolCategories = this.categories.filter(category => category.isProduct);

    return this.datacolCategories.filter(category => ids.includes(category.id));
  }

  /**
   * Ottiene le categorie filtrate in base ai tag del cliente attivo (OTTIMIZZATO)
   * Questo metodo utilizza query database ottimizzate per performance migliori
   */
  async getFilteredCategoriesByTags(categories?: Category[], idCatalog: number = 1): Promise<Category[]> {
    try {
      console.log('🚀 [CATEGORY_SERVICE] Caricamento categorie filtrate ottimizzato');
      const startTime = Date.now();

      // Verifica se il filtraggio è abilitato
      if (!this._tagFilterService.isFilteringEnabled()) {
        console.log('🏷️ [CATEGORY_SERVICE] Filtraggio disabilitato');
        return categories || await this._dbService.getAll(["categories"]);
      }

      // Utilizza il nuovo metodo ottimizzato che fa il filtraggio a livello database
      const filteredCategories = await this._tagFilterService.getFilteredCategories(idCatalog);

      const endTime = Date.now();
      console.log(`✅ [CATEGORY_SERVICE] Categorie filtrate in ${endTime - startTime}ms: ${filteredCategories.length} risultati`);

      return filteredCategories;

    } catch (error) {
      console.error('❌ Errore nel filtraggio categorie per tag:', error);
      // In caso di errore, restituisce tutte le categorie passate o caricate
      return categories || await this._dbService.getAll(["categories"]);
    }
  }

  /**
   * Ottiene le categorie filtrate in base ai tag del cliente attivo (METODO LEGACY)
   * Mantiene la compatibilità con il vecchio sistema per eventuali fallback
   * @deprecated Utilizzare getFilteredCategoriesByTags() per performance migliori
   */
  async getFilteredCategoriesByTagsLegacy(categories?: Category[]): Promise<Category[]> {
    try {
      // Se non vengono passate categorie, carica tutte dal database
      if (!categories) {
        categories = await this._dbService.getAll(["categories"]);
      }

      // Verifica se il filtraggio è abilitato
      if (!this._tagFilterService.isFilteringEnabled()) {
        return categories;
      }

      // Applica i filtri tag (metodo legacy)
      const filterResult = await this._tagFilterService.applyFiltersToCategories(categories);

      // Filtra le categorie in base al risultato
      const filteredCategories = categories.filter(category =>
        filterResult.visibleCategories.includes(category.id)
      );

      console.log(`🔍 [CATEGORY_SERVICE] Filtrate (legacy) ${filteredCategories.length}/${categories.length} categorie`);
      return filteredCategories;

    } catch (error) {
      console.error('❌ Errore nel filtraggio categorie (legacy):', error);
      // In caso di errore, restituisce tutte le categorie
      return categories || [];
    }
  }

  /**
   * Ottiene solo le DatacolCategory (prodotti) filtrate
   */
  async getFilteredDatacolCategories(categories?: Category[]): Promise<Category[]> {
    try {
      const allFilteredCategories = await this.getFilteredCategoriesByTags(categories);

      // Filtra solo le DatacolCategory (isProduct = true)
      const datacolCategories = allFilteredCategories.filter(category => category.isProduct);

      console.log(`📦 Trovate ${datacolCategories.length} DatacolCategory filtrate`);
      return datacolCategories;

    } catch (error) {
      console.error('❌ Errore nel filtraggio DatacolCategory:', error);
      return [];
    }
  }

  /**
   * Verifica se una categoria ha figli visibili dopo il filtraggio
   */
  async categoryHasVisibleChildren(parentCategory: Category, allCategories?: Category[]): Promise<boolean> {
    try {
      if (!allCategories) {
        allCategories = await this._dbService.getAll(["categories"]);
      }

      // Se il filtraggio non è abilitato, considera tutti i figli come visibili
      if (!this._tagFilterService.isFilteringEnabled()) {
        return this.hasAnyChildren(parentCategory, allCategories);
      }

      // Trova le categorie figlie
      const children = this.getChildCategories(parentCategory, allCategories);

      // Applica i filtri ai figli
      const filterResult = await this._tagFilterService.applyFiltersToCategories(children);

      // Verifica se almeno un figlio è visibile
      return filterResult.visibleCategories.length > 0;

    } catch (error) {
      console.error('❌ Errore nella verifica figli visibili:', error);
      return false;
    }
  }

  /**
   * Ottiene le categorie figlie di una categoria padre
   */
  private getChildCategories(parentCategory: Category, allCategories: Category[]): Category[] {
    return allCategories.filter(cat =>
      cat.idParent === parentCategory.id ||
      (cat.idApp && parentCategory.idApp &&
       cat.idApp.startsWith(parentCategory.idApp) &&
       cat.id !== parentCategory.id)
    );
  }

  /**
   * Verifica se una categoria ha figli (senza considerare i filtri)
   */
  private hasAnyChildren(parentCategory: Category, allCategories: Category[]): boolean {
    return this.getChildCategories(parentCategory, allCategories).length > 0;
  }

  /**
   * Ottiene le root categories filtrate (solo quelle con figli visibili) - OTTIMIZZATO
   */
  async getFilteredRootCategories(idCatalog: number = 1): Promise<Category[]> {
    try {
      console.log('🚀 [CATEGORY_SERVICE] Caricamento root categories filtrate ottimizzato');
      const startTime = Date.now();

      if (!this._tagFilterService.isFilteringEnabled()) {
        console.log('🏷️ [CATEGORY_SERVICE] Filtraggio disabilitato per root categories');
        const allCategories = await this._dbService.getAll(["categories"]);
        return allCategories.filter(cat =>
          cat.isRootCategory === 'true' || cat.isRootCategoryByRegex === 'true'
        );
      }

      // Utilizza il nuovo metodo ottimizzato
      const filteredRootCategories = await this._tagFilterService.getFilteredRootCategories(idCatalog);

      const endTime = Date.now();
      console.log(`✅ [CATEGORY_SERVICE] Root categories filtrate in ${endTime - startTime}ms: ${filteredRootCategories.length} risultati`);

      return filteredRootCategories;

    } catch (error) {
      console.error('❌ Errore nel filtraggio root categories:', error);
      return [];
    }
  }

  /**
   * Ottiene le root categories filtrate (METODO LEGACY)
   * @deprecated Utilizzare getFilteredRootCategories() per performance migliori
   */
  async getFilteredRootCategoriesLegacy(): Promise<Category[]> {
    try {
      const allCategories = await this._dbService.getAll(["categories"]);
      const rootCategories = allCategories.filter(cat =>
        cat.isRootCategory === 'true' || cat.isRootCategoryByRegex === 'true'
      );

      if (!this._tagFilterService.isFilteringEnabled()) {
        return rootCategories;
      }

      // Filtra le root categories che hanno almeno un figlio visibile
      const filteredRootCategories: Category[] = [];

      for (const rootCategory of rootCategories) {
        const hasVisibleChildren = await this.categoryHasVisibleChildren(rootCategory, allCategories);
        if (hasVisibleChildren) {
          filteredRootCategories.push(rootCategory);
        }
      }

      // Log delle root categories visibili/nascoste
      const visibleRootNames = filteredRootCategories.map(cat => cat.name);
      const hiddenRootNames = rootCategories
        .filter(cat => !filteredRootCategories.includes(cat))
        .map(cat => cat.name);

      console.log('🌳 [ROOT_FILTER] Root categories visibili (legacy):', visibleRootNames);
      console.log('🌳 [ROOT_FILTER] Root categories nascoste (legacy):', hiddenRootNames);
      console.log(`🌳 [ROOT_FILTER] Totale (legacy): ${filteredRootCategories.length}/${rootCategories.length} root categories visibili`);

      return filteredRootCategories;

    } catch (error) {
      console.error('❌ Errore nel filtraggio root categories (legacy):', error);
      return [];
    }
  }

  async getProductCounter(idsubcategory:string, idrootcategory){
    let subcategory:Subcategory = null;
    await this._dbService.getRecordsByANDCondition('subcategories', [{key: 'idsubcategory', value: idsubcategory},{key: 'idrootcategory', value: idrootcategory}]).then((subcategories: Subcategory[]) => {
      if(!!subcategories && subcategories.length > 0)
        subcategory = subcategories[0];
    });
    return subcategory;
  }

  public async catalogByProduct(subcategories: Category[]){
    this.categories = [];
    await this.explodeHierarchy(subcategories, true);
    return this.categories;
  }

  private explodeHierarchy(categories: Category[], byProduct: boolean = false){
    if (categories && categories.length > 0) {
      categories.forEach(async (element: Category) => {
        const found = this.categories.find((item) => item.id === element.id);
        // if(!found)
        {
          if(byProduct)
          {
            if(!element.isProduct){
              if(!!element.subcategories)
              {
                const subcategories = Utils.objToJson(element.subcategories);
                if(!!subcategories && subcategories.length > 0)
                {
                  if(subcategories.filter((item:Category) =>{ return item.isProduct }).length > 0){
                    // Aggiungo un elemento di tipo categoria solo se ha dei figli prodotto, altrimenti evito
                    this.categories = this.categories.concat(element);
                  }
                  await this.explodeHierarchy(Utils.objToJson(subcategories), byProduct);
                }
              }
            }
            else if(element.isProduct) {
              this.categories = this.categories.concat(element);
            }
          }
          else
          {
            this.categories = this.categories.concat(element);
            if(!!element.subcategories)
            {
              const subcategories = Utils.objToJson(element.subcategories);
              if(!!subcategories && subcategories.length > 0)
              {
                await this.explodeHierarchy(Utils.objToJson(element.subcategories), byProduct);
              }
            }
          }
        }
      });
    }
  }

  public getProductFromNavigationHierarchy(id:string, items: Category[], navigationQueue: Selection[]){
    let lastCategoryChildren = items;
    let lastCategory: Category | null = null;
    // let lastCategoryBrothers = null;
    // la navigazione è gerarchica
    if(navigationQueue.length === 0)
    {
      // lastCategoryBrothers = [...lastCategoryChildren];
      lastCategory = items.find((item) => item.id.toString() === id);
      lastCategoryChildren = Utils.objToJson(lastCategory.subcategories);
    }
    else {
      let found = false;
      navigationQueue.forEach((element) => {
        if(!found)
        {
          // lastCategoryBrothers = [...lastCategoryChildren];
          lastCategory = lastCategoryChildren.find((item) => item.id.toString() === element.id.toString());
          lastCategoryChildren = Utils.objToJson(lastCategory.subcategories);
          if(!!lastCategory &&  !!lastCategory.id && lastCategory.id.toString() === id)
            found = true;
        }
      });
    }
    return lastCategory;
  }

  private findCategoryInSubcategories(categories:Category[], idCategory: string){
    if(!!categories)
    {
      return categories.find(item => item.id.toString() === idCategory);
    }
    return null;
  }

  private removeDuplicates(dirtyArray:Category[]) {
    var result = dirtyArray.reduce((unique, o) => {
      if(!unique.some((obj:Category) => obj.id.toString() === o.id.toString())) {
        unique.push(o);
      }
      return unique;
    },[]);
    return result;
  }

  private async findProductsInCategory(categories: Category[]) : Promise<Category[]> {
    let datacolCategories: Category[] = [];
    if (categories && categories.length > 0)  {
      await categories.reduce(async (promise, category) => {
        await promise;
          if(category.isProduct) {
            datacolCategories = [category].concat(datacolCategories);
          }
          else {
            const subcategory = !!category.subcategories ? Utils.objToJson(category.subcategories) : [];
            if(subcategory.length > 0)
            {
              const list = await this.findProductsInCategory(subcategory).then(result => result);
              datacolCategories = [...datacolCategories, ...list];
            }
          }
        }, Promise.resolve());
    }
    return new Promise((resolve) => { resolve(datacolCategories); });
  }

  // public async isFavorite(idCategory: string, customerUid: string) {
  //   var favorites = [];
  //     const finished = await this._dbService.getRecordsByANDCondition('favorites', [{key: 'customerUid', value: customerUid}]).then((data: Favorite[]) => {
  //       if(data.length > 0) {
  //         favorites = data;
  //       }
  //     });
  //   const filtered: Favorite[] = favorites.filter((favorite) => favorite.idCategory.toString() === idCategory);
  //   const found = filtered.length === 1;
  //   return found;
  // }


}
