import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonButton, IonCard, IonCardContent, IonCardHeader, IonCardTitle, IonItem, IonLabel, IonList, IonSpinner } from '@ionic/angular/standalone';
import { TagFilterService } from '../../../private/services/tag-filter.service';
import { CategoryService } from '../../../service/category/category.service';
import { HybridDbService } from '../../hybrid-db.service';

interface PerformanceTestResult {
  method: string;
  duration: number;
  categoriesCount: number;
  success: boolean;
  error?: string;
}

@Component({
  selector: 'app-filtering-performance-test',
  standalone: true,
  imports: [
    CommonModule,
    IonCard,
    IonCardHeader,
    IonCardTitle,
    IonCardContent,
    IonButton,
    IonList,
    IonItem,
    IonLabel,
    IonSpinner
  ],
  template: `
    <ion-card>
      <ion-card-header>
        <ion-card-title>🚀 Test Performance Filtraggio Categorie</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <div class="test-controls">
          <ion-button 
            (click)="runPerformanceTests()" 
            [disabled]="isRunning"
            color="primary">
            <ion-spinner *ngIf="isRunning" name="crescent"></ion-spinner>
            {{ isRunning ? 'Test in corso...' : 'Avvia Test Performance' }}
          </ion-button>
          
          <ion-button 
            (click)="getFilteringStats()" 
            [disabled]="isRunning"
            color="secondary">
            Statistiche Database
          </ion-button>
        </div>

        <div *ngIf="stats" class="stats-section">
          <h3>📊 Statistiche Database</h3>
          <ion-list>
            <ion-item>
              <ion-label>
                <h3>Categorie Totali</h3>
                <p>{{ stats.totalCategories }}</p>
              </ion-label>
            </ion-item>
            <ion-item>
              <ion-label>
                <h3>Categorie Prodotto</h3>
                <p>{{ stats.productCategories }}</p>
              </ion-label>
            </ion-item>
            <ion-item>
              <ion-label>
                <h3>Root Categories</h3>
                <p>{{ stats.rootCategories }}</p>
              </ion-label>
            </ion-item>
            <ion-item>
              <ion-label>
                <h3>Tag Totali</h3>
                <p>{{ stats.totalTags }}</p>
              </ion-label>
            </ion-item>
            <ion-item>
              <ion-label>
                <h3>Categorie con Tag</h3>
                <p>{{ stats.categoriesWithTags }}</p>
              </ion-label>
            </ion-item>
            <ion-item>
              <ion-label>
                <h3>Cache Size</h3>
                <p>{{ stats.cacheSize }}</p>
              </ion-label>
            </ion-item>
          </ion-list>
        </div>

        <div *ngIf="testResults.length > 0" class="results-section">
          <h3>📈 Risultati Test Performance</h3>
          <ion-list>
            <ion-item *ngFor="let result of testResults">
              <ion-label>
                <h3>{{ result.method }}</h3>
                <p>
                  <strong>Durata:</strong> {{ result.duration }}ms | 
                  <strong>Categorie:</strong> {{ result.categoriesCount }} |
                  <strong>Status:</strong> 
                  <span [class]="result.success ? 'success' : 'error'">
                    {{ result.success ? '✅ Successo' : '❌ Errore' }}
                  </span>
                </p>
                <p *ngIf="result.error" class="error-message">{{ result.error }}</p>
              </ion-label>
            </ion-item>
          </ion-list>
        </div>

        <div *ngIf="performanceComparison" class="comparison-section">
          <h3>⚡ Confronto Performance</h3>
          <div class="performance-metrics">
            <div class="metric">
              <strong>Miglioramento:</strong> 
              <span class="improvement">{{ performanceComparison.improvement }}%</span>
            </div>
            <div class="metric">
              <strong>Metodo più veloce:</strong> 
              <span class="fastest">{{ performanceComparison.fastest }}</span>
            </div>
          </div>
        </div>
      </ion-card-content>
    </ion-card>
  `,
  styles: [`
    .test-controls {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
    }

    .stats-section, .results-section, .comparison-section {
      margin-top: 20px;
    }

    .success {
      color: var(--ion-color-success);
    }

    .error {
      color: var(--ion-color-danger);
    }

    .error-message {
      color: var(--ion-color-danger);
      font-size: 0.9em;
    }

    .performance-metrics {
      display: flex;
      gap: 20px;
      margin-top: 10px;
    }

    .metric {
      padding: 10px;
      background: var(--ion-color-light);
      border-radius: 8px;
    }

    .improvement {
      color: var(--ion-color-success);
      font-weight: bold;
    }

    .fastest {
      color: var(--ion-color-primary);
      font-weight: bold;
    }
  `]
})
export class FilteringPerformanceTestComponent {
  private _tagFilterService = inject(TagFilterService);
  private _categoryService = inject(CategoryService);
  private _dbService = inject(HybridDbService);

  isRunning = false;
  testResults: PerformanceTestResult[] = [];
  stats: any = null;
  performanceComparison: any = null;

  async runPerformanceTests(): Promise<void> {
    this.isRunning = true;
    this.testResults = [];
    this.performanceComparison = null;

    try {
      console.log('🚀 Avvio test performance filtraggio categorie...');

      // Test 1: Metodo ottimizzato
      const optimizedResult = await this.testOptimizedFiltering();
      this.testResults.push(optimizedResult);

      // Test 2: Metodo legacy (se disponibile)
      const legacyResult = await this.testLegacyFiltering();
      this.testResults.push(legacyResult);

      // Test 3: Root categories ottimizzate
      const rootOptimizedResult = await this.testOptimizedRootFiltering();
      this.testResults.push(rootOptimizedResult);

      // Calcola il confronto delle performance
      this.calculatePerformanceComparison();

      console.log('✅ Test performance completati');

    } catch (error) {
      console.error('❌ Errore durante i test performance:', error);
    } finally {
      this.isRunning = false;
    }
  }

  private async testOptimizedFiltering(): Promise<PerformanceTestResult> {
    try {
      const startTime = Date.now();
      const categories = await this._tagFilterService.getFilteredCategories(1);
      const endTime = Date.now();

      return {
        method: 'Filtraggio Ottimizzato (Database)',
        duration: endTime - startTime,
        categoriesCount: categories.length,
        success: true
      };
    } catch (error) {
      return {
        method: 'Filtraggio Ottimizzato (Database)',
        duration: 0,
        categoriesCount: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Errore sconosciuto'
      };
    }
  }

  private async testLegacyFiltering(): Promise<PerformanceTestResult> {
    try {
      const startTime = Date.now();
      const categories = await this._categoryService.getFilteredCategoriesByTagsLegacy();
      const endTime = Date.now();

      return {
        method: 'Filtraggio Legacy (In-Memory)',
        duration: endTime - startTime,
        categoriesCount: categories.length,
        success: true
      };
    } catch (error) {
      return {
        method: 'Filtraggio Legacy (In-Memory)',
        duration: 0,
        categoriesCount: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Errore sconosciuto'
      };
    }
  }

  private async testOptimizedRootFiltering(): Promise<PerformanceTestResult> {
    try {
      const startTime = Date.now();
      const rootCategories = await this._tagFilterService.getFilteredRootCategories(1);
      const endTime = Date.now();

      return {
        method: 'Root Categories Ottimizzate',
        duration: endTime - startTime,
        categoriesCount: rootCategories.length,
        success: true
      };
    } catch (error) {
      return {
        method: 'Root Categories Ottimizzate',
        duration: 0,
        categoriesCount: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Errore sconosciuto'
      };
    }
  }

  private calculatePerformanceComparison(): void {
    const optimized = this.testResults.find(r => r.method.includes('Ottimizzato (Database)'));
    const legacy = this.testResults.find(r => r.method.includes('Legacy'));

    if (optimized && legacy && optimized.success && legacy.success) {
      const improvement = Math.round(((legacy.duration - optimized.duration) / legacy.duration) * 100);
      const fastest = optimized.duration < legacy.duration ? 'Ottimizzato' : 'Legacy';

      this.performanceComparison = {
        improvement: improvement > 0 ? improvement : 0,
        fastest
      };
    }
  }

  async getFilteringStats(): Promise<void> {
    try {
      this.stats = await this._tagFilterService.getFilteringPerformanceStats(1);
      console.log('📊 Statistiche filtraggio:', this.stats);
    } catch (error) {
      console.error('❌ Errore nel recupero statistiche:', error);
    }
  }
}
