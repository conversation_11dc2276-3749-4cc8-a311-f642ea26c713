# Sistema di Filtraggio per Tag - Documentazione Tecnica

## Panoramica

Il sistema di filtraggio per tag è un componente fondamentale dell'applicazione mobile Datacol che permette di filtrare le categorie di prodotti in base ai tag gerarchici associati ai clienti. Il sistema utilizza una gerarchia a 3 livelli (Settori/Attività/Professioni) più un tag universale per mostrare solo le categorie rilevanti per ogni cliente specifico.

## Architettura del Sistema

### Componenti Principali

1. **TagFilterService** - Servizio principale per la gestione dei filtri
2. **TagFilterPanelComponent** - Interfaccia utente per la configurazione dei filtri
3. **Store NgRx** - Gestione dello stato globale dei filtri
4. **Database Locale** - Persistenza offline dei dati di filtraggio
5. **Servizi di Sincronizzazione** - Sincronizzazione dei tag e gerarchia

### Flusso di Dati

```
API Backend → Sync Services → Database Locale → TagFilterService → NgRx Store → UI Components
```

## Modelli di Dati

### CustomerTagFilter
```typescript
interface CustomerTagFilter {
  customerUid: string;        // UID del cliente
  settore?: string;          // Tag del settore (es. "AL-*")
  attivita?: string;         // Codice attività (es. "20-AD_*")
  professione?: string;      // Tag professione (es. "09-PD_*")
  universale: boolean;       // Include sempre il tag universale
  customTags: string[];      // Tag aggiunti manualmente dall'agente
  isDefault: boolean;        // Se true, usa solo i tag predefiniti
}
```

### TagFilterState
```typescript
interface TagFilterState {
  activeCustomerUid: string | null;           // Cliente attualmente attivo
  currentFilter: CustomerTagFilter | null;    // Filtro corrente
  availableConfigurations: FilterConfiguration[]; // Configurazioni salvate
  isFilteringEnabled: boolean;                // Se il filtraggio è abilitato
  lastFilterUpdate: number;                   // Timestamp ultimo aggiornamento
}
```

### Tag Hierarchy
```typescript
interface Tag {
  idTag: number;
  keyTag: string;           // Chiave univoca del tag
  description: string;      // Descrizione leggibile
  subTags: Tag[];          // Sottotag gerarchici
  checked?: boolean;       // Stato di selezione nell'UI
}
```

## Database Schema

### Tabelle Principali

#### customer_filter_state
Memorizza lo stato del filtro per ogni cliente:
```sql
CREATE TABLE customer_filter_state (
  customerUid TEXT PRIMARY KEY,
  currentFilterId TEXT,
  isFilteringEnabled INTEGER NOT NULL,
  lastFilterUpdate INTEGER NOT NULL,
  settore TEXT,
  attivita TEXT,
  professione TEXT,
  customTags TEXT  -- JSON array
);
```

#### datacol_category_tags
Cache locale dei tag assegnati alle categorie:
```sql
CREATE TABLE datacol_category_tags (
  idSubCategory INTEGER NOT NULL,
  idCatalog INTEGER NOT NULL,
  keyTag TEXT NOT NULL,
  description TEXT,
  lastSync INTEGER NOT NULL
);
```

#### sector_hierarchy
Gerarchia dei settori per il filtraggio:
```sql
CREATE TABLE sector_hierarchy (
  idCatalog INTEGER NOT NULL,
  idTag INTEGER NOT NULL,
  keyTag TEXT NOT NULL,
  description TEXT NOT NULL,
  parentIdTag INTEGER,
  level INTEGER NOT NULL,  -- 1=Professione, 2=Attività, 3=Settore
  lastSync INTEGER NOT NULL
);
```

#### filter_configurations
Configurazioni di filtri personalizzati:
```sql
CREATE TABLE filter_configurations (
  id TEXT PRIMARY KEY,
  customerUid TEXT NOT NULL,
  name TEXT NOT NULL,
  activeTags TEXT NOT NULL,  -- JSON array
  isDefault INTEGER,
  createdAt INTEGER NOT NULL,
  lastUsed INTEGER NOT NULL
);
```

## Logica di Filtraggio

### Gerarchia dei Tag

Il sistema utilizza una gerarchia a 3 livelli:

1. **Professione** (Livello 1): Codici come "09-PD_*"
2. **Attività** (Livello 2): Codici come "20-AD_*"  
3. **Settore** (Livello 3): Codici come "AL-*"
4. **Universale**: Tag speciale "TS_Tutti_i_settori" sempre incluso

### Mappatura Cliente → Tag

Il sistema mappa automaticamente i dati del cliente ai tag:

```typescript
// Esempio di mappatura
const customer = {
  division: "AL",           // → "AL-*" (settore)
  codiceAttivita: "20-AD",  // → "20-AD_*" (attività)
  codiceProfessione: "09"   // → "09-PD_*" (professione)
};
```

### Algoritmo di Filtraggio

1. **Recupero Tag Categoria**: Per ogni categoria, recupera i tag dal database locale
2. **Creazione Tag Attivi**: Combina tag predefiniti del cliente + tag personalizzati
3. **Verifica Visibilità**: Una categoria è visibile se almeno uno dei suoi tag è presente nei tag attivi
4. **Filtraggio Gerarchico**: Le categorie padre sono visibili solo se hanno figli visibili

```typescript
// Pseudocodice algoritmo
async isCategoryVisible(category: Category, filter: CustomerTagFilter): Promise<boolean> {
  const categoryTags = await getCategoryTags(category);
  const activeTags = getActiveTagsFromFilter(filter);
  
  return categoryTags.some(tag => activeTags.includes(tag.keyTag));
}
```

## Sincronizzazione

### API Utilizzate

1. **getSectorHierarchy**: Recupera la gerarchia completa dei settori
2. **syncDCTags**: Sincronizza i tag assegnati alle categorie DataCol

### Processo di Sync

1. **Sync v2 Orchestrator** chiama le API durante la sincronizzazione principale
2. I dati vengono salvati nel database locale per funzionamento offline
3. I componenti utilizzano SOLO i dati dal database locale, mai chiamate API dirette

```typescript
// Esempio processo sync
async syncTagsAndHierarchy(idCatalog: number): Promise<void> {
  // 1. Sincronizza gerarchia settori
  const hierarchyResponse = await getSectorHierarchy(idCatalog);
  await saveSectorHierarchyToDatabase(idCatalog, hierarchyResponse.content);
  
  // 2. Sincronizza tag categorie
  const tagsResponse = await syncDCTags(idCatalog);
  await saveDCTagsToDatabase(idCatalog, tagsResponse.content);
}
```

## Utilizzo del Sistema

### Inizializzazione

```typescript
// 1. Imposta cliente attivo
await tagFilterService.setActiveCustomer(customerUid);

// 2. Il servizio crea automaticamente il filtro predefinito
// 3. Lo stato viene salvato nel database per persistenza
```

### Applicazione Filtri

```typescript
// Applica filtri alle categorie
const result = await tagFilterService.applyFiltersToCategories(categories);

console.log(`Visibili: ${result.totalVisible}, Nascoste: ${result.totalHidden}`);
```

### Gestione Tag Personalizzati

```typescript
// Aggiungi tag personalizzato
await tagFilterService.addCustomTag('CUSTOM_TAG_KEY');

// Rimuovi tag personalizzato  
await tagFilterService.removeCustomTag('CUSTOM_TAG_KEY');

// Applica modifiche
await tagFilterService.applyCurrentFilter();
```

## Interfaccia Utente

### TagFilterPanelComponent

Componente principale per la gestione dei filtri con:

- **Toggle Filtraggio**: Abilita/disabilita il sistema di filtraggio
- **Tag Predefiniti**: Mostra i tag automatici del cliente (non modificabili)
- **Tag Personalizzati**: Permette aggiunta/rimozione di tag custom
- **Gerarchia Settori**: Interfaccia ad albero per selezione tag gerarchici
- **Pulsanti Azione**: Applica filtri / Reset configurazione

### Localizzazione

Il componente supporta la localizzazione tramite chiavi i18n:
- `TAG_FILTER.TITLE`
- `TAG_FILTER.ENABLE_FILTERING`
- `TAG_FILTER.APPLY_FILTERS`
- `TAG_FILTER.RESET_FILTERS`

## Performance e Caching

### Cache in Memoria
- Cache dei tag categoria con validità 5 minuti
- Evita query ripetute al database per le stesse categorie

### Indici Database
- Indici ottimizzati per query frequenti
- Indici composti per ricerche multi-campo

### Funzionamento Offline
- Tutti i dati necessari sono memorizzati localmente
- Nessuna dipendenza da connessione internet dopo la sincronizzazione

## Gestione Errori

Il sistema implementa gestione robusta degli errori:

1. **Fallback Graceful**: Se il filtraggio fallisce, mostra tutte le categorie
2. **Logging Dettagliato**: Log strutturati per debugging
3. **Validazione Dati**: Controlli sui dati prima dell'elaborazione
4. **Recovery Automatico**: Ricreazione database in caso di corruzione

## Considerazioni di Sicurezza

- I filtri sono applicati lato client per performance
- I tag predefiniti del cliente non possono essere rimossi
- Le configurazioni sono associate al cliente specifico
- Persistenza locale sicura tramite database criptato (mobile)

## Estensibilità

Il sistema è progettato per essere estensibile:

- Nuovi livelli gerarchici possono essere aggiunti facilmente
- Supporto per filtri complessi tramite configurazioni personalizzate
- API modulare per integrazione con altri sistemi di filtraggio

## Esempi Pratici

### Scenario 1: Cliente con Settore Specifico

```typescript
// Cliente: Settore Alimentare, Attività Distribuzione, Professione Direttore
const customer = {
  uid: "customer-123",
  division: "AL",
  codiceAttivita: "20-AD",
  codiceProfessione: "09"
};

// Filtro automatico generato:
const filter = {
  customerUid: "customer-123",
  settore: "AL-*",           // Settore Alimentare
  attivita: "20-AD_*",       // Attività Distribuzione
  professione: "09-PD_*",    // Professione Direttore
  universale: true,          // Tag universale sempre incluso
  customTags: [],            // Nessun tag personalizzato inizialmente
  isDefault: true
};
```

### Scenario 2: Aggiunta Tag Personalizzati

```typescript
// L'agente aggiunge tag personalizzati per il cliente
await tagFilterService.addCustomTag("SETTORE_CHIMICO");
await tagFilterService.addCustomTag("PRODOTTI_SPECIALI");

// Il filtro viene aggiornato:
const updatedFilter = {
  ...filter,
  customTags: ["SETTORE_CHIMICO", "PRODOTTI_SPECIALI"],
  isDefault: false  // Non più configurazione predefinita
};
```

### Scenario 3: Verifica Visibilità Categoria

```typescript
// Categoria con tag assegnati
const category = {
  id: "cat-001",
  name: "Prodotti Alimentari",
  idSubCategory: 12345,
  idCatalog: 1
};

// Tag della categoria (dal database):
const categoryTags = [
  { keyTag: "AL-FOOD", description: "Alimentari - Cibo" },
  { keyTag: "TS_Tutti_i_settori", description: "Universale" }
];

// Tag attivi del filtro:
const activeTags = ["AL-*", "20-AD_*", "09-PD_*", "UNIVERSALE"];

// Verifica visibilità:
const isVisible = categoryTags.some(tag =>
  activeTags.some(activeTag =>
    tag.keyTag.startsWith(activeTag.replace('*', '')) ||
    tag.keyTag === "TS_Tutti_i_settori"
  )
);
// Risultato: true (match con tag universale)
```

## Troubleshooting

### Problema: Categorie Non Filtrate Correttamente

**Sintomi**: Le categorie non vengono filtrate nonostante il filtraggio sia abilitato

**Possibili Cause**:
1. Tag non sincronizzati nel database locale
2. Mappatura cliente → tag non corretta
3. Cache corrotta

**Soluzioni**:
```typescript
// 1. Verifica sincronizzazione tag
const tags = await dbService.getRecordsByANDCondition(
  'datacol_category_tags',
  [{ key: 'idCatalog', value: '1' }]
);
console.log('Tag sincronizzati:', tags.length);

// 2. Forza re-sincronizzazione
await syncroV2Service.syncTagsAndHierarchy(catalogId);

// 3. Pulisci cache
tagFilterService.clearCache();
```

### Problema: Performance Lente

**Sintomi**: Il filtraggio impiega troppo tempo

**Soluzioni**:
1. Verifica indici database
2. Ottimizza cache
3. Riduci frequenza applicazione filtri

```typescript
// Verifica performance
console.time('filter-performance');
const result = await tagFilterService.applyFiltersToCategories(categories);
console.timeEnd('filter-performance');
```

### Problema: Stato Filtro Non Persistente

**Sintomi**: I filtri si resettano al riavvio dell'app

**Soluzioni**:
```typescript
// Verifica salvataggio stato
await tagFilterService.saveCustomerFilterState(customerUid, filter);

// Verifica caricamento stato
const savedState = await tagFilterService.loadCustomerFilterState(customerUid);
console.log('Stato salvato:', savedState);
```

### Problema: Tag Gerarchia Non Caricati

**Sintomi**: L'interfaccia di selezione tag è vuota

**Soluzioni**:
```typescript
// Verifica gerarchia nel database
const hierarchy = await tagFilterService.getSectorHierarchyFromDatabase(catalogId);
console.log('Gerarchia caricata:', hierarchy.tags.length);

// Forza ri-sincronizzazione gerarchia
await syncroV2Service.syncSectorHierarchy(catalogId);
```

## Log e Debugging

### Abilitazione Log Dettagliati

Il sistema utilizza log strutturati con prefissi specifici:

- `🏷️ [TAG_FILTER]` - Operazioni generali di filtraggio
- `🔍 [OFFLINE_FILTER]` - Operazioni database offline
- `💾 [SYNC_TAGS]` - Sincronizzazione tag
- `⚠️ [TAG_FILTER]` - Warning e problemi non critici
- `❌ [TAG_FILTER]` - Errori critici

### Monitoraggio Performance

```typescript
// Abilita monitoraggio performance
const startTime = performance.now();
const result = await tagFilterService.applyFiltersToCategories(categories);
const endTime = performance.now();
console.log(`Filtraggio completato in ${endTime - startTime}ms`);
```

## Migrazione e Aggiornamenti

### Aggiornamento Schema Database

Quando si aggiungono nuove funzionalità:

1. Aggiornare la versione del database
2. Implementare script di migrazione
3. Testare compatibilità con dati esistenti

### Backward Compatibility

Il sistema mantiene compatibilità con versioni precedenti:
- Gestione graceful di campi mancanti
- Valori di default per nuove proprietà
- Migrazione automatica configurazioni esistenti

## Metriche e Monitoraggio

### KPI Importanti

- Tempo medio di applicazione filtri
- Percentuale di categorie filtrate
- Frequenza di utilizzo tag personalizzati
- Tasso di errore sincronizzazione

### Logging Metriche

```typescript
// Esempio logging metriche
console.log(`📊 [METRICS] Filtro applicato: ${result.totalVisible}/${result.totalVisible + result.totalHidden} categorie visibili (${Math.round(result.totalVisible / (result.totalVisible + result.totalHidden) * 100)}%)`);
```
