import { <PERSON>mpo<PERSON>, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { 
  IonContent, 
  IonHeader, 
  IonTitle, 
  IonToolbar, 
  IonBackButton, 
  IonButtons,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonButton,
  IonItem,
  IonLabel,
  IonList,
  IonSpinner,
  IonBadge,
  IonText
} from '@ionic/angular/standalone';
import { TagFilterService } from '../../services/tag-filter.service';
import { CategoryService } from '../../../service/category/category.service';
import { HybridDbService } from '../../../shared/hybrid-db.service';

@Component({
  selector: 'app-simple-filtering-test',
  templateUrl: './simple-filtering-test.page.html',
  styleUrls: ['./simple-filtering-test.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonContent,
    IonHeader,
    IonTitle,
    IonToolbar,
    IonBackButton,
    IonButtons,
    IonCard,
    IonCardContent,
    IonCardHeader,
    IonCardTitle,
    IonButton,
    IonItem,
    IonLabel,
    IonList,
    IonSpinner,
    IonBadge,
    IonText
  ]
})
export class SimpleFilteringTestPage implements OnInit {
  private _tagFilterService = inject(TagFilterService);
  private _categoryService = inject(CategoryService);
  private _dbService = inject(HybridDbService);

  isLoading = false;
  testResults: any[] = [];
  currentFilter: any = null;
  filteringEnabled = false;

  ngOnInit() {
    this.loadCurrentState();
  }

  private loadCurrentState() {
    this.filteringEnabled = this._tagFilterService.isFilteringEnabled();
    this.currentFilter = this._tagFilterService.getCurrentFilter();
  }

  async testSimpleFiltering() {
    this.isLoading = true;
    this.testResults = [];

    try {
      console.log('🧪 Test filtraggio SEMPLIFICATO...');

      // Test 1: Verifica che il metodo semplificato funzioni senza errori
      const startTime1 = Date.now();
      const filteredCategories = await this._tagFilterService.getFilteredCategories(1);
      const endTime1 = Date.now();

      this.testResults.push({
        test: 'Filtraggio Semplificato - Categorie',
        status: 'success',
        message: `${filteredCategories.length} categorie caricate`,
        details: {
          duration: endTime1 - startTime1,
          categoriesCount: filteredCategories.length
        }
      });

      // Test 2: Verifica root categories
      const startTime2 = Date.now();
      const rootCategories = await this._tagFilterService.getFilteredRootCategories(1);
      const endTime2 = Date.now();

      this.testResults.push({
        test: 'Filtraggio Semplificato - Root Categories',
        status: 'success',
        message: `${rootCategories.length} root categories caricate`,
        details: {
          duration: endTime2 - startTime2,
          rootCategoriesCount: rootCategories.length
        }
      });

      // Test 3: Verifica CategoryService
      const startTime3 = Date.now();
      const serviceCategories = await this._categoryService.getFilteredCategoriesByTags(undefined, 1);
      const endTime3 = Date.now();

      this.testResults.push({
        test: 'CategoryService Semplificato',
        status: 'success',
        message: `${serviceCategories.length} categorie dal service`,
        details: {
          duration: endTime3 - startTime3,
          categoriesCount: serviceCategories.length
        }
      });

      // Test 4: Verifica statistiche
      const stats = await this._tagFilterService.getFilteringPerformanceStats(1);

      this.testResults.push({
        test: 'Statistiche Database',
        status: 'success',
        message: `${stats.totalCategories} categorie totali, ${stats.rootCategories} root`,
        details: {
          totalCategories: stats.totalCategories,
          rootCategories: stats.rootCategories,
          productCategories: stats.productCategories,
          totalTags: stats.totalTags
        }
      });

      console.log('✅ Test filtraggio semplificato completati');

    } catch (error) {
      console.error('❌ Errore durante i test:', error);
      this.testResults.push({
        test: 'Errore',
        status: 'error',
        message: error instanceof Error ? error.message : 'Errore sconosciuto',
        details: { error: error }
      });
    } finally {
      this.isLoading = false;
    }
  }

  async testRefresh() {
    try {
      console.log('🔄 Test refresh...');
      this._tagFilterService.forceRefreshCategories();
      
      // Attendi un momento per il refresh
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      this.testResults.push({
        test: 'Test Refresh',
        status: 'success',
        message: 'Refresh eseguito correttamente',
        details: { refreshed: true }
      });

    } catch (error) {
      this.testResults.push({
        test: 'Test Refresh',
        status: 'error',
        message: error instanceof Error ? error.message : 'Errore refresh',
        details: { error: error }
      });
    }
  }

  toggleFiltering() {
    this._tagFilterService.setFilteringEnabled(!this.filteringEnabled);
    this.loadCurrentState();
  }

  async clearResults() {
    this.testResults = [];
  }
}
