.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.test-buttons ion-button {
  margin: 0;
}

ion-card {
  margin: 16px;
}

ion-card-title {
  font-size: 1.2em;
  font-weight: 600;
}

.details {
  margin-top: 8px;
  padding: 8px;
  background: var(--ion-color-light);
  border-radius: 4px;
}

.details p {
  margin: 2px 0;
}

.details small {
  color: var(--ion-color-medium);
}

ion-text[color="success"] {
  color: var(--ion-color-success);
}

ion-text[color="danger"] {
  color: var(--ion-color-danger);
}

h4 {
  margin-top: 16px;
  margin-bottom: 8px;
  color: var(--ion-color-primary);
}

ul {
  margin: 8px 0;
  padding-left: 20px;
}

li {
  margin: 4px 0;
}
