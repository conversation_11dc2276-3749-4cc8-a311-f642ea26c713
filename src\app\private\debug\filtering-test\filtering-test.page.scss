.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.test-buttons ion-button {
  margin: 0;
}

ion-card {
  margin: 16px;
}

ion-card-title {
  font-size: 1.2em;
  font-weight: 600;
}

.performance-metrics {
  display: flex;
  gap: 20px;
  margin-top: 10px;
  flex-wrap: wrap;
}

.metric {
  padding: 10px;
  background: var(--ion-color-light);
  border-radius: 8px;
  flex: 1;
  min-width: 150px;
}

.success {
  color: var(--ion-color-success);
}

.error {
  color: var(--ion-color-danger);
}

.improvement {
  color: var(--ion-color-success);
  font-weight: bold;
}

.fastest {
  color: var(--ion-color-primary);
  font-weight: bold;
}
