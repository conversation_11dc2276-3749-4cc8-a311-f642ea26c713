<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/private/debug"></ion-back-button>
    </ion-buttons>
    <ion-title>🚀 Test Filtraggio Semplificato</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Test Filtraggio Semplificato</ion-title>
    </ion-toolbar>
  </ion-header>

  <!-- Stato Corrente -->
  <ion-card>
    <ion-card-header>
      <ion-card-title>📊 Stato Sistema</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-list>
        <ion-item>
          <ion-label>
            <h3>Filtraggio Abilitato</h3>
            <p>{{ filteringEnabled ? 'Sì' : 'No' }}</p>
          </ion-label>
          <ion-badge [color]="filteringEnabled ? 'success' : 'medium'">
            {{ filteringEnabled ? 'ON' : 'OFF' }}
          </ion-badge>
        </ion-item>
        
        <ion-item *ngIf="currentFilter">
          <ion-label>
            <h3>Filtro Attivo</h3>
            <p><strong>Cliente:</strong> {{ currentFilter.customerUid || 'N/A' }}</p>
            <p><strong>Tag Custom:</strong> {{ currentFilter.customTags?.length || 0 }}</p>
          </ion-label>
        </ion-item>
      </ion-list>
    </ion-card-content>
  </ion-card>

  <!-- Controlli Test -->
  <ion-card>
    <ion-card-header>
      <ion-card-title>🧪 Test Semplificati</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <div class="test-buttons">
        <ion-button 
          (click)="testSimpleFiltering()" 
          [disabled]="isLoading"
          color="primary"
          expand="block">
          <ion-spinner *ngIf="isLoading" name="crescent"></ion-spinner>
          {{ isLoading ? 'Test in corso...' : 'Test Filtraggio Semplificato' }}
        </ion-button>

        <ion-button 
          (click)="testRefresh()" 
          [disabled]="isLoading"
          color="secondary"
          expand="block">
          Test Refresh
        </ion-button>

        <ion-button 
          (click)="toggleFiltering()" 
          [disabled]="isLoading"
          [color]="filteringEnabled ? 'danger' : 'success'"
          expand="block">
          {{ filteringEnabled ? 'Disabilita' : 'Abilita' }} Filtraggio
        </ion-button>

        <ion-button 
          (click)="clearResults()" 
          [disabled]="isLoading"
          color="medium"
          expand="block">
          Pulisci Risultati
        </ion-button>
      </div>
    </ion-card-content>
  </ion-card>

  <!-- Risultati Test -->
  <ion-card *ngIf="testResults.length > 0">
    <ion-card-header>
      <ion-card-title>📈 Risultati Test</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-list>
        <ion-item *ngFor="let result of testResults">
          <ion-label>
            <h3>{{ result.test }}</h3>
            <p>
              <ion-text [color]="result.status === 'success' ? 'success' : 'danger'">
                <strong>{{ result.status === 'success' ? '✅' : '❌' }}</strong>
                {{ result.message }}
              </ion-text>
            </p>
            <div *ngIf="result.details" class="details">
              <p *ngIf="result.details.duration"><small><strong>Durata:</strong> {{ result.details.duration }}ms</small></p>
              <p *ngIf="result.details.categoriesCount"><small><strong>Categorie:</strong> {{ result.details.categoriesCount }}</small></p>
              <p *ngIf="result.details.rootCategoriesCount"><small><strong>Root Categories:</strong> {{ result.details.rootCategoriesCount }}</small></p>
              <p *ngIf="result.details.totalCategories"><small><strong>Totale Categorie:</strong> {{ result.details.totalCategories }}</small></p>
              <p *ngIf="result.details.productCategories"><small><strong>Categorie Prodotto:</strong> {{ result.details.productCategories }}</small></p>
              <p *ngIf="result.details.totalTags"><small><strong>Tag Totali:</strong> {{ result.details.totalTags }}</small></p>
              <p *ngIf="result.details.refreshed"><small><strong>Refresh:</strong> Completato</small></p>
            </div>
          </ion-label>
        </ion-item>
      </ion-list>
    </ion-card-content>
  </ion-card>

  <!-- Informazioni -->
  <ion-card>
    <ion-card-header>
      <ion-card-title>ℹ️ Sistema Semplificato</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-text>
        <h4>Logica Semplificata:</h4>
        <ul>
          <li><strong>Tag solo per Root Categories:</strong> I tag sono applicati solo alle categorie root</li>
          <li><strong>Filtraggio Diretto:</strong> Se una root category ha i tag richiesti, mostra lei e TUTTE le sue sottocategorie/prodotti</li>
          <li><strong>Nessuna Ricorsione:</strong> Eliminata la logica complessa che causava stack overflow</li>
          <li><strong>Performance Ottimizzate:</strong> Query dirette senza calcoli complessi</li>
        </ul>
        
        <h4>Vantaggi:</h4>
        <ul>
          <li>✅ Nessun Stack Overflow</li>
          <li>✅ Performance migliori</li>
          <li>✅ Logica più chiara</li>
          <li>✅ Manutenzione semplificata</li>
        </ul>
      </ion-text>
    </ion-card-content>
  </ion-card>

</ion-content>
