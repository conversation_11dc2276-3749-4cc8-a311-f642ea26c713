<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/private/debug"></ion-back-button>
    </ion-buttons>
    <ion-title>✅ Validazione Filtraggio</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Validazione Filtraggio</ion-title>
    </ion-toolbar>
  </ion-header>

  <!-- Stato Corrente -->
  <ion-card>
    <ion-card-header>
      <ion-card-title>📊 Stato Filtraggio</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-list>
        <ion-item>
          <ion-label>
            <h3>Filtraggio Abilitato</h3>
            <p>{{ filteringEnabled ? 'Sì' : 'No' }}</p>
          </ion-label>
          <ion-badge [color]="filteringEnabled ? 'success' : 'medium'">
            {{ filteringEnabled ? 'ON' : 'OFF' }}
          </ion-badge>
        </ion-item>
        
        <ion-item *ngIf="currentFilter">
          <ion-label>
            <h3>Filtro Attivo</h3>
            <p><strong>Cliente:</strong> {{ currentFilter.customerUid || 'N/A' }}</p>
            <p><strong>Settore:</strong> {{ currentFilter.settore || 'N/A' }}</p>
            <p><strong>Attività:</strong> {{ currentFilter.attivita || 'N/A' }}</p>
            <p><strong>Professione:</strong> {{ currentFilter.professione || 'N/A' }}</p>
            <p><strong>Tag Custom:</strong> {{ currentFilter.customTags?.length || 0 }}</p>
          </ion-label>
        </ion-item>
      </ion-list>
    </ion-card-content>
  </ion-card>

  <!-- Controlli Test -->
  <ion-card>
    <ion-card-header>
      <ion-card-title>🧪 Test di Validazione</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <div class="test-buttons">
        <ion-button 
          (click)="runValidationTests()" 
          [disabled]="isLoading"
          color="primary"
          expand="block">
          <ion-spinner *ngIf="isLoading" name="crescent"></ion-spinner>
          {{ isLoading ? 'Test in corso...' : 'Esegui Test Validazione' }}
        </ion-button>

        <ion-button 
          (click)="toggleFiltering()" 
          [disabled]="isLoading"
          [color]="filteringEnabled ? 'danger' : 'success'"
          expand="block">
          {{ filteringEnabled ? 'Disabilita' : 'Abilita' }} Filtraggio
        </ion-button>

        <div class="button-row">
          <ion-button 
            (click)="addTestTag()" 
            [disabled]="isLoading"
            color="secondary"
            size="small">
            Aggiungi Tag Test
          </ion-button>

          <ion-button 
            (click)="removeTestTag()" 
            [disabled]="isLoading"
            color="warning"
            size="small">
            Rimuovi Tag Test
          </ion-button>
        </div>
      </div>
    </ion-card-content>
  </ion-card>

  <!-- Risultati Validazione -->
  <ion-card *ngIf="validationResults.length > 0">
    <ion-card-header>
      <ion-card-title>📈 Risultati Validazione</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-list>
        <ion-item *ngFor="let result of validationResults">
          <ion-label>
            <h3>{{ result.test }}</h3>
            <p>
              <ion-text [color]="result.status === 'success' ? 'success' : result.status === 'warning' ? 'warning' : 'danger'">
                <strong>{{ result.status === 'success' ? '✅' : result.status === 'warning' ? '⚠️' : '❌' }}</strong>
                {{ result.message }}
              </ion-text>
            </p>
            <div *ngIf="result.details" class="details">
              <p><small><strong>Durata:</strong> {{ result.details.duration }}ms</small></p>
              <p *ngIf="result.details.totalCategories"><small><strong>Categorie Totali:</strong> {{ result.details.totalCategories }}</small></p>
              <p *ngIf="result.details.validProductCategories !== undefined"><small><strong>Categorie Prodotto Valide:</strong> {{ result.details.validProductCategories }}</small></p>
              <p *ngIf="result.details.invalidProductCategories !== undefined"><small><strong>Categorie Prodotto Invalide:</strong> {{ result.details.invalidProductCategories }}</small></p>
              <p *ngIf="result.details.nonProductCategories !== undefined"><small><strong>Categorie Non-Prodotto:</strong> {{ result.details.nonProductCategories }}</small></p>
              <p *ngIf="result.details.activeTags !== undefined"><small><strong>Tag Attivi:</strong> {{ result.details.activeTags }}</small></p>
              <p *ngIf="result.details.totalRootCategories !== undefined"><small><strong>Root Categories Totali:</strong> {{ result.details.totalRootCategories }}</small></p>
              <p *ngIf="result.details.validRootCategories !== undefined"><small><strong>Root Categories Valide:</strong> {{ result.details.validRootCategories }}</small></p>
              <p *ngIf="result.details.invalidRootCategories !== undefined"><small><strong>Root Categories Invalide:</strong> {{ result.details.invalidRootCategories }}</small></p>
            </div>
          </ion-label>
        </ion-item>
      </ion-list>
    </ion-card-content>
  </ion-card>

  <!-- Informazioni Aggiuntive -->
  <ion-card>
    <ion-card-header>
      <ion-card-title>ℹ️ Informazioni</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-text>
        <p>Questa pagina valida che il sistema di filtraggio ottimizzato funzioni correttamente:</p>
        <ul>
          <li><strong>Categorie Filtrate:</strong> Verifica che solo le categorie con tag corretti siano visibili</li>
          <li><strong>Root Categories:</strong> Verifica che le root categories abbiano figli visibili</li>
          <li><strong>Refresh:</strong> Verifica che il sistema di refresh funzioni correttamente</li>
        </ul>
        <p><small>I test vengono eseguiti utilizzando i metodi ottimizzati implementati.</small></p>
      </ion-text>
    </ion-card-content>
  </ion-card>

</ion-content>
