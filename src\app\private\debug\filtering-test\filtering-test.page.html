<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-back-button defaultHref="/private/debug"></ion-back-button>
    </ion-buttons>
    <ion-title>🧪 Test Filtraggio Ottimizzato</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-header collapse="condense">
    <ion-toolbar>
      <ion-title size="large">Test Filtraggio</ion-title>
    </ion-toolbar>
  </ion-header>

  <!-- Stato Corrente -->
  <ion-card>
    <ion-card-header>
      <ion-card-title>📊 Stato Corrente</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-list>
        <ion-item>
          <ion-label>
            <h3>Filtraggio Abilitato</h3>
            <p>{{ filteringEnabled ? 'Sì' : 'No' }}</p>
          </ion-label>
          <ion-badge [color]="filteringEnabled ? 'success' : 'medium'">
            {{ filteringEnabled ? 'ON' : 'OFF' }}
          </ion-badge>
        </ion-item>
        
        <ion-item *ngIf="currentFilter">
          <ion-label>
            <h3>Filtro Attivo</h3>
            <p>Cliente: {{ currentFilter.customerUid || 'N/A' }}</p>
            <p>Settore: {{ currentFilter.settore || 'N/A' }}</p>
            <p>Attività: {{ currentFilter.attivita || 'N/A' }}</p>
            <p>Professione: {{ currentFilter.professione || 'N/A' }}</p>
          </ion-label>
        </ion-item>
      </ion-list>
    </ion-card-content>
  </ion-card>

  <!-- Controlli Test -->
  <ion-card>
    <ion-card-header>
      <ion-card-title>🔧 Controlli Test</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <div class="test-buttons">
        <ion-button 
          (click)="testBasicFiltering()" 
          [disabled]="isLoading"
          color="primary"
          expand="block">
          <ion-spinner *ngIf="isLoading" name="crescent"></ion-spinner>
          {{ isLoading ? 'Test in corso...' : 'Test Filtraggio Base' }}
        </ion-button>

        <ion-button 
          (click)="initializeIndexes()" 
          [disabled]="isLoading"
          color="secondary"
          expand="block">
          Inizializza Indici DB
        </ion-button>

        <ion-button 
          (click)="toggleFiltering()" 
          [disabled]="isLoading"
          [color]="filteringEnabled ? 'danger' : 'success'"
          expand="block">
          {{ filteringEnabled ? 'Disabilita' : 'Abilita' }} Filtraggio
        </ion-button>
      </div>
    </ion-card-content>
  </ion-card>

  <!-- Risultati Test Base -->
  <ion-card *ngIf="testResults.length > 0">
    <ion-card-header>
      <ion-card-title>📈 Risultati Test Base</ion-card-title>
    </ion-card-header>
    <ion-card-content>
      <ion-list>
        <ion-item *ngFor="let result of testResults">
          <ion-label>
            <h3>{{ result.test }}</h3>
            <p>
              <strong>Durata:</strong> {{ result.duration }}ms |
              <strong>Elementi:</strong> {{ result.count }} |
              <strong>Status:</strong> 
              <span [style.color]="result.success ? 'green' : 'red'">
                {{ result.success ? '✅ Successo' : '❌ Errore' }}
              </span>
            </p>
            <p *ngIf="result.error" style="color: red;">{{ result.error }}</p>
          </ion-label>
        </ion-item>
      </ion-list>
    </ion-card-content>
  </ion-card>

  <!-- Componente Test Performance Avanzato -->
  <app-filtering-performance-test></app-filtering-performance-test>

</ion-content>
