import { <PERSON>mpo<PERSON>, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { 
  IonContent, 
  IonHeader, 
  IonTitle, 
  IonToolbar, 
  IonBackButton, 
  IonButtons,
  IonCard,
  IonCardContent,
  IonCardHeader,
  IonCardTitle,
  IonButton,
  IonItem,
  IonLabel,
  IonList,
  IonSpinner,
  IonBadge
} from '@ionic/angular/standalone';
import { FilteringPerformanceTestComponent } from '../../../shared/components/filtering-performance-test/filtering-performance-test.component';
import { TagFilterService } from '../../services/tag-filter.service';
import { CategoryService } from '../../../service/category/category.service';

@Component({
  selector: 'app-filtering-test',
  templateUrl: './filtering-test.page.html',
  styleUrls: ['./filtering-test.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonContent,
    IonHeader,
    IonTitle,
    IonToolbar,
    IonBackButton,
    IonButtons,
    IonCard,
    IonCardContent,
    IonCardHeader,
    IonCardTitle,
    IonButton,
    IonItem,
    IonLabel,
    IonList,
    IonSpinner,
    IonBadge,
    FilteringPerformanceTestComponent
  ]
})
export class FilteringTestPage implements OnInit {
  private _tagFilterService = inject(TagFilterService);
  private _categoryService = inject(CategoryService);

  isLoading = false;
  filteringEnabled = false;
  currentFilter: any = null;
  testResults: any[] = [];

  ngOnInit() {
    this.loadCurrentState();
  }

  private loadCurrentState() {
    this.filteringEnabled = this._tagFilterService.isFilteringEnabled();
    this.currentFilter = this._tagFilterService.getCurrentFilter();
  }

  async testBasicFiltering() {
    this.isLoading = true;
    this.testResults = [];

    try {
      console.log('🧪 Avvio test filtraggio di base...');

      // Test 1: Caricamento categorie ottimizzato
      const startTime1 = Date.now();
      const optimizedCategories = await this._tagFilterService.getFilteredCategories(1);
      const endTime1 = Date.now();

      this.testResults.push({
        test: 'Categorie Ottimizzate',
        duration: endTime1 - startTime1,
        count: optimizedCategories.length,
        success: true
      });

      // Test 2: Caricamento root categories ottimizzato
      const startTime2 = Date.now();
      const optimizedRootCategories = await this._tagFilterService.getFilteredRootCategories(1);
      const endTime2 = Date.now();

      this.testResults.push({
        test: 'Root Categories Ottimizzate',
        duration: endTime2 - startTime2,
        count: optimizedRootCategories.length,
        success: true
      });

      // Test 3: Metodo CategoryService ottimizzato
      const startTime3 = Date.now();
      const serviceCategories = await this._categoryService.getFilteredCategoriesByTags(undefined, 1);
      const endTime3 = Date.now();

      this.testResults.push({
        test: 'CategoryService Ottimizzato',
        duration: endTime3 - startTime3,
        count: serviceCategories.length,
        success: true
      });

      console.log('✅ Test filtraggio completati:', this.testResults);

    } catch (error) {
      console.error('❌ Errore durante i test:', error);
      this.testResults.push({
        test: 'Errore',
        duration: 0,
        count: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Errore sconosciuto'
      });
    } finally {
      this.isLoading = false;
    }
  }

  async initializeIndexes() {
    this.isLoading = true;
    try {
      console.log('🔧 Inizializzazione indici...');
      await this._tagFilterService.initializeFilteringIndexes();
      console.log('✅ Indici inizializzati con successo');
    } catch (error) {
      console.error('❌ Errore nell\'inizializzazione indici:', error);
    } finally {
      this.isLoading = false;
    }
  }

  toggleFiltering() {
    this._tagFilterService.setFilteringEnabled(!this.filteringEnabled);
    this.loadCurrentState();
  }
}
